import React from 'react';
import { cn } from '@/utils/utlis';
import { socialItems, SocialItem } from '@/constants/socialMedia';

interface SocialMediaSelectProps {
  label?: string;
  mode?: 'single' | 'multiple';
  value?: string | string[];
  onChange?: ((selectedId: string) => void) | ((selectedIds: string[]) => void);
  error?: string;
  className?: string;
  minSelection?: number;
}

const SocialMediaSelect: React.FC<SocialMediaSelectProps> = ({
  label,
  mode = 'multiple', // Default to multiple mode for backward compatibility
  value,
  onChange,
  error,
  className,
  minSelection = 1,
}) => {
  // Normalize value to always be an array for internal logic
  const normalizedValue = React.useMemo(() => {
    if (mode === 'single') {
      return value
        ? [typeof value === 'string' ? value : value[0]]
        : ['twitter'];
    }
    return Array.isArray(value) ? value : value ? [value] : ['twitter'];
  }, [value, mode]);

  const handleItemClick = (itemValue: string) => {
    if (!onChange) return;

    if (mode === 'single') {
      // Single mode: just select the clicked item
      (onChange as (selectedId: string) => void)(itemValue);
    } else {
      // Multiple mode: existing logic
      // Prevent deselecting if it would go below minimum selection
      if (
        normalizedValue.length === minSelection &&
        normalizedValue.includes(itemValue)
      ) {
        return;
      }

      const newSelection = normalizedValue.includes(itemValue)
        ? normalizedValue.filter((id) => id !== itemValue)
        : [...normalizedValue, itemValue];

      (onChange as (selectedIds: string[]) => void)(newSelection);
    }
  };

  return (
    <div className={cn('w-full', className)}>
      {label && (
        <label className="mb-2 block font-medium text-neutral-300">
          {label}
        </label>
      )}

      <div className="flex w-full flex-wrap justify-center gap-5">
        {socialItems.map((item: SocialItem) => {
          const Icon = item.icon;
          const isSelected = normalizedValue.includes(item.value);
          const isDisabled = false; // Only Twitter (ID 3) is enabled

          return (
            <div
              key={item.id}
              className={cn(
                'flex h-[175px] w-[175px] shrink-0 flex-col items-center justify-between rounded-lg border-2 bg-neutral-900 p-6 text-center shadow-lg transition-colors select-none sm:w-[calc(50%-0.625rem)] md:w-[175px]',
                isDisabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer',
                isSelected ? 'border-primary-400' : 'border-neutral-700',
                error && 'border-red-500'
              )}
              onClick={() => handleItemClick(item.value)}
            >
              <Icon
                size={46}
                className={cn(
                  isDisabled ? 'fill-neutral-600' : 'fill-primary-400'
                )}
              />
              <div
                className={cn(
                  'text-lg font-bold',
                  isDisabled ? 'text-neutral-600' : 'text-white'
                )}
              >
                {item.title}
              </div>
              <div
                className={cn(
                  'text-sm font-bold',
                  isDisabled ? 'text-neutral-700' : 'text-neutral-500'
                )}
              >
                {item.description}
              </div>
            </div>
          );
        })}
      </div>

      {error && <p className="mt-2 text-xs text-red-500">{error}</p>}
    </div>
  );
};

export default SocialMediaSelect;
