import { useNavigate } from 'react-router-dom';
import Breadcrumb from '@/components/ui/Breadcrumb';
import SocialMediaSelect from '@/components/ui/SocialMediaSelect';
import { useState, useCallback, useEffect } from 'react';
import Select, { SelectOption } from '@/components/ui/Select';
import TextInput from '@/components/ui/TextInput';
import Button from '@/components/ui/Button';
import Collapsible from '@/components/ui/Collapsible';
import SearchDrawerInput from '@/components/ui/SearchDrawerInput';
import TimePeriodPicker from '@/components/ui/TimePeriodPicker';
import TagInput from '@/components/ui/TagInput';
import SourceInput from '@/components/ui/SourceInput';
import { CreateWidgetPayload, Widget } from '@/types/dashboard';

// Define the mapping between report types and their allowed chart types
const REPORT_CHART_MAPPING = {
  process: ['bar', 'bar_stack', 'bar_comp', 'line', 'table'],
  'statistical%view': ['badge'],
  'statistical%like': ['badge'],
  'statistical%comment': ['badge'],
  'statistical%retweet': ['badge'],
  top_sources: ['table', 'bar_stack_hor', 'bar_stack_ver', 'radial'],
  sentiments: [
    'pie',
    'donut',
    'semi_pie',
    'bar_stack_hor',
    'bar_stack_ver',
    'table',
    'radar',
    'spider',
    // 'wind',
  ],
} as const;

// Define which chart types are stack charts (only for multiple socials)
const STACK_CHART_TYPES = [
  'bar_stack',
  'bar_comp',
  'bar_stack_hor',
  'bar_stack_ver',
] as const;

const SINGLE_CHART_TYPES = [
  'table',
  'badge',
  'bar',
  'pie',
  'donut',
  'semi_pie',
] as const;

const ZERO_LVL_CHART_TYPES = ['badge'] as const;

// Sample sources data
const sampleSources = [
  {
    user_name: 'john_doe',
    display_name: 'John Doe',
    platform: 'twitter',
    followers_count: 1500,
    verified: false,
  },
  {
    user_name: 'jane_smith',
    display_name: 'Jane Smith',
    platform: 'instagram',
    followers_count: 2300,
    verified: true,
  },
  {
    user_name: 'tech_news',
    display_name: 'Tech News',
    platform: 'telegram',
    followers_count: 5000,
    verified: false,
  },
];

interface WidgetFormProps {
  dashboardId: string;
  widgetId?: string;
  initialData?: Widget;
  breadcrumbItems: Array<{ label: string; href?: string }>;
  onSubmit: (payload: CreateWidgetPayload) => Promise<void>;
  submitButtonText: string;
  submittingText: string;
  title: string;
  description: string;
}

export default function WidgetForm({
  dashboardId,
  widgetId,
  initialData,
  breadcrumbItems,
  onSubmit,
  submitButtonText,
  submittingText,
  title: pageTitle,
  description,
}: WidgetFormProps) {
  const navigate = useNavigate();
  const [selectedSocials, setSelectedSocials] = useState<string[]>(['twitter']);
  const [selectedReport, setSelectedReport] = useState<string>('');
  const [selectedChartType, setSelectedChartType] = useState<string>('');
  const [selectedTimeInterval, setSelectedTimeInterval] = useState<string>('');
  const [title, setTitle] = useState<string>('');
  const [error, setError] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  // Advanced filters state
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [hashtags, setHashtags] = useState<string[]>([]);
  const [selectedSources, setSelectedSources] = useState<string[]>([]);
  const [timePeriodValue, setTimePeriodValue] = useState<number>(
    24 * 60 * 60 * 1000
  ); // 24 hours in milliseconds

  // Initialize form with existing data if editing
  useEffect(() => {
    if (initialData) {
      setTitle(initialData.title);
      setSelectedReport(initialData.report_type);
      setSelectedChartType(initialData.chart_type);
      setSelectedSocials(initialData.params.query?.platform || ['twitter']);
      setSearchQuery(initialData.params.query?.q || '');
      setHashtags(initialData.params.query?.hashtags || []);
      setSelectedSources(initialData.params.query?.sources || []);
      setSelectedTimeInterval(
        (initialData.params.runtime.interval / 60).toString()
      ); // Convert seconds to minutes
      setTimePeriodValue(initialData.params.runtime.gap || 24 * 60 * 60 * 1000);
    }
  }, [initialData]);

  // Define report options
  const reportOptions: SelectOption[] = [
    { label: 'روند انتشار محتوا طی زمان', value: 'process' },
    { label: 'تعداد بازدید', value: 'statistical%view' },
    { label: 'تعداد لایک', value: 'statistical%like' },
    { label: 'تعداد بازنشر', value: 'statistical%retweet' },
    { label: 'تعداد نظرات', value: 'statistical%comment' },
    { label: 'برترین منابع (کاربران)', value: 'top_sources' },
    { label: 'تحلیل احساسات محتوای منتشر شده', value: 'sentiments' },
  ];

  // Define time interval options
  const timeIntervalOptions: SelectOption[] = [
    { label: '5 دقیقه', value: '5' },
    { label: '10 دقیقه', value: '10' },
    { label: '15 دقیقه', value: '15' },
    { label: '30 دقیقه', value: '30' },
    { label: '1 ساعت', value: '60' },
    { label: '2 ساعت', value: '120' },
    { label: '6 ساعت', value: '360' },
    { label: '12 ساعت', value: '720' },
    { label: '24 ساعت', value: '1440' },
  ];

  const getChartTypeOptions = useCallback((): SelectOption[] => {
    if (!selectedReport) return [];

    const chartTypes =
      REPORT_CHART_MAPPING[
        selectedReport as keyof typeof REPORT_CHART_MAPPING
      ] || [];

    // Filter chart types based on selected socials
    const filteredChartTypes = chartTypes.filter((chartType) => {
      // If it's a stack chart, only allow if multiple socials are selected
      if (STACK_CHART_TYPES.includes(chartType as any)) {
        return selectedSocials.length > 1;
      }

      // If it's a single chart, only allow if single social is selected
      if (SINGLE_CHART_TYPES.includes(chartType as any)) {
        return selectedSocials.length === 1;
      }

      // For other chart types, allow regardless of social count
      return true;
    });

    return filteredChartTypes.map((type) => ({
      value: type,
      label: getChartTypeLabel(type),
    }));
  }, [selectedReport, selectedSocials]);

  const getChartTypeLabel = (type: string): string => {
    const labels: Record<string, string> = {
      bar: 'نمودار میله‌ای',
      badge: 'برچسب',
      bar_stack: 'نمودار میله‌ای انباشته',
      bar_comp: 'نمودار میله‌ای مقایسه‌ای',
      line: 'نمودار خطی',
      table: 'جدول',
      bar_stack_hor: 'نمودار میله‌ای افقی انباشته',
      bar_stack_ver: 'نمودار میله‌ای عمودی انباشته',
      radial: 'نمودار شعاعی',
      pie: 'نمودار دایره‌ای',
      donut: 'نمودار حلقه‌ای',
      semi_pie: 'نمودار نیم دایره',
      radar: 'نمودار راداری',
      spider: 'نمودار عنکبوتی',
      wind: 'نمودار بادی',
    };
    return labels[type] || type;
  };

  // Event handlers
  const handleSocialMediaSelectChange = useCallback(
    (socials: string[]) => {
      setSelectedSocials(socials);

      // Reset chart type if current selection is not valid for new social selection
      if (selectedChartType) {
        const isStackChart = STACK_CHART_TYPES.includes(
          selectedChartType as any
        );
        const isSingleChart = SINGLE_CHART_TYPES.includes(
          selectedChartType as any
        );

        if (
          (isStackChart && socials.length <= 1) ||
          (isSingleChart && socials.length > 1)
        ) {
          setSelectedChartType('');
        }
      }
    },
    [selectedChartType]
  );

  const handleReportChange = useCallback((value: string) => {
    setSelectedReport(value);
    setSelectedChartType(''); // Reset chart type when report changes
  }, []);

  const handleChartTypeChange = useCallback((value: string) => {
    setSelectedChartType(value);
  }, []);

  const handleTimeIntervalChange = useCallback((value: string) => {
    setSelectedTimeInterval(value);
  }, []);

  const handleTitleChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setTitle(e.target.value);
    },
    []
  );

  const handleTimePeriodChange = useCallback((value: number) => {
    setTimePeriodValue(value);
  }, []);

  const validateForm = (): boolean => {
    if (!title.trim()) {
      setError('عنوان نمودار الزامی است');
      return false;
    }
    if (!selectedReport) {
      setError('انتخاب نوع گزارش الزامی است');
      return false;
    }
    if (!selectedChartType) {
      setError('انتخاب نوع نمودار الزامی است');
      return false;
    }
    if (!selectedTimeInterval) {
      setError('انتخاب وقفه زمانی الزامی است');
      return false;
    }
    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsSubmitting(true);
    setError('');

    try {
      // Build query params for advanced filters
      const queryParams: any = {};

      if (searchQuery.trim()) {
        queryParams.q = searchQuery.trim();
      }

      if (hashtags.length > 0) {
        queryParams.hashtags = hashtags;
      }

      if (selectedSources.length > 0) {
        queryParams.sources = selectedSources;
      }

      const payload: CreateWidgetPayload = {
        title: title.trim(),
        chart_type: selectedChartType,
        report_type: selectedReport,
        params: {
          runtime: {
            interval: parseInt(selectedTimeInterval) * 60, // Convert minutes to seconds
            gap: timePeriodValue, // Add time period from advanced filters
          },
          position: initialData?.params.position || {
            x: 0,
            y: 0,
            width: 5,
            height: 5,
          },
          query: {
            platform: selectedSocials,
            ...queryParams,
          },
        },
      };

      await onSubmit(payload);

      // Navigate back to dashboard detail page
      navigate(`/dashboard/${dashboardId}`);
    } catch (error) {
      console.error('Error submitting form:', error);
      setError(error instanceof Error ? error.message : 'خطا در عملیات');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-full p-8">
      <Breadcrumb items={breadcrumbItems} />
      <div className="mx-auto mt-8 w-full max-w-7xl space-y-6">
        {/* Social Media Select - Full Width */}
        <div className="w-full">
          <SocialMediaSelect
            mode="multiple"
            label="بستر‌های مورد نظر خود را انتخاب کنید"
            value={selectedSocials}
            onChange={handleSocialMediaSelectChange}
            error={error}
          />
        </div>

        {/* Report Selection Section - Collapsible */}
        <Collapsible
          title="انتخاب نوع گزارش"
          defaultOpen={true}
          className="mt-8"
        >
          {/* Text Input and Time Interval Select - Half Width Each */}
          <div className="mb-6 flex items-center gap-4">
            <div className="flex-6">
              <TextInput
                label="عنوان نمودار"
                placeholder="نمودار برترین محتوا منتشر شده در اینستاگرام"
                value={title}
                onChange={handleTitleChange}
              />
            </div>
            <div className="flex-6">
              <Select
                label="وقفه زمانی به‌روزرسانی گزارش‌ها"
                placeholder="انتخاب کنید"
                options={timeIntervalOptions}
                value={selectedTimeInterval}
                onChange={handleTimeIntervalChange}
              />
            </div>
          </div>

          {/* Report and Chart Type Selects */}
          <div className="flex items-center gap-4">
            <div className="flex-6">
              <Select
                label="گزارش‌های آماری و هوش مصنوعی"
                placeholder="انتخاب نوع گزارش"
                options={reportOptions}
                value={selectedReport}
                onChange={handleReportChange}
              />
            </div>
            <div className="flex-6">
              <Select
                label="نوع نمودار"
                placeholder={
                  selectedReport
                    ? 'انتخاب نوع نمودار'
                    : 'ابتدا نوع گزارش را انتخاب کنید'
                }
                options={getChartTypeOptions()}
                value={selectedChartType}
                onChange={handleChartTypeChange}
              />
            </div>
          </div>
        </Collapsible>

        {/* Advanced Filters - Collapsible */}
        <Collapsible title="فیلترهای پیشرفته" defaultOpen={false}>
          <SearchDrawerInput
            label="عبارت جستجو"
            placeholder="عبارت جستجو خود را وارد کنید"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />

          <TagInput
            label="هشتگ‌ها"
            tags={hashtags}
            onTagsChange={setHashtags}
            placeholder="هشتگ‌های مورد نظر را وارد کنید"
          />

          <SourceInput
            label="منابع"
            sources={selectedSources}
            onSourcesChange={setSelectedSources}
            availableSources={sampleSources}
            placeholder="منابع مورد نظر را انتخاب کنید"
          />

          <TimePeriodPicker
            value={timePeriodValue}
            onChange={handleTimePeriodChange}
            label="بازه زمانی نتایج گزارش"
          />
        </Collapsible>

        {/* Error Display */}
        {error && (
          <div className="mt-4 rounded-md bg-red-50 p-4">
            <p className="text-sm text-red-800">{error}</p>
          </div>
        )}

        {/* Submit Button */}
        <div className="mt-8 flex justify-end gap-4">
          <Button
            variant="secondary"
            onClick={() => navigate(`/dashboard/${dashboardId}`)}
            disabled={isSubmitting}
          >
            انصراف
          </Button>
          <Button
            variant="primary"
            onClick={handleSubmit}
            disabled={isSubmitting}
          >
            {isSubmitting ? submittingText : submitButtonText}
          </Button>
        </div>
      </div>
    </div>
  );
}
