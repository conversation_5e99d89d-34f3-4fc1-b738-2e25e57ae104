import apiClient from './apiClient';
import {
  CreateDashboardPayload,
  CreateDashboardResponse,
  Dashboard,
  DashboardsListResponse,
  CreateWidgetPayload,
  CreateWidgetResponse,
  Widget,
  WidgetDataRequest,
  WidgetDataResponse,
  WidgetData,
} from '@/types/dashboard';
import * as z from 'zod';

// Zod schema for validating the create dashboard payload
export const createDashboardSchema = z.object({
  title: z
    .string()
    .min(3, { message: 'عنوان باید حداقل ۳ کاراکتر داشته باشد.' }),
  description: z.string().optional(),
  params: z.object({
    runtime: z.object({
      gap: z.number().positive({ message: 'بازه زمانی باید مثبت باشد.' }),
    }),
    query: z.object({
      q: z.string().min(2, { message: 'عبارت جستجو الزامی است.' }),
      hashtags: z.array(z.string()).optional(),
      sources: z.array(z.string()).optional(),
    }),
  }),
});

// Zod schema for validating the create widget payload
export const createWidgetSchema = z.object({
  title: z
    .string()
    .min(3, { message: 'عنوان باید حداقل ۳ کاراکتر داشته باشد.' }),
  chart_type: z.string().min(1, { message: 'نوع نمودار الزامی است.' }),
  report_type: z.string().min(1, { message: 'نوع گزارش الزامی است.' }),
  params: z.object({
    runtime: z.object({
      interval: z.number().positive({ message: 'بازه زمانی باید مثبت باشد.' }),
    }),
    position: z.object({
      x: z
        .number()
        .int()
        .min(0, { message: 'موقعیت x باید عدد صحیح غیر منفی باشد.' }),
      y: z
        .number()
        .int()
        .min(0, { message: 'موقعیت y باید عدد صحیح غیر منفی باشد.' }),
      width: z
        .number()
        .int()
        .positive({ message: 'عرض باید عدد صحیح مثبت باشد.' }),
      height: z
        .number()
        .int()
        .positive({ message: 'ارتفاع باید عدد صحیح مثبت باشد.' }),
    }),
    query: z.object({
      platform: z
        .array(z.string())
        .min(1, { message: 'بستر مورد نظر الزامی است.' }),
      q: z.string().optional(),
      hashtags: z.array(z.string()).optional(),
      sources: z.array(z.string()).optional(),
    }),
  }),
});

export const createDashboard = async (
  payload: CreateDashboardPayload
): Promise<Dashboard> => {
  try {
    // Validate payload with Zod
    const validatedPayload = createDashboardSchema.parse(payload);

    const response = await apiClient.post<CreateDashboardResponse>(
      '/dashboard/',
      validatedPayload
    );

    if (!response.data.data) {
      throw new Error('Invalid response format');
    }

    return response.data.data;
  } catch (error: unknown) {
    console.error('Create dashboard error:', error);

    // Handle Zod validation errors
    if (error instanceof z.ZodError) {
      const errorMessages = error.issues
        .map((issue) => issue.message)
        .join(', ');
      throw new Error(`خطا در اعتبارسنجی داده‌ها: ${errorMessages}`);
    }

    // Handle different types of API errors
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as { response: { status: number; data?: any } };
      if (axiosError.response?.status === 400) {
        throw new Error('اطلاعات وارد شده نامعتبر است');
      } else if (axiosError.response?.status === 401) {
        throw new Error('شما مجاز به انجام این عملیات نیستید');
      } else if (axiosError.response?.status === 500) {
        throw new Error('خطا در سرور. لطفا بعدا تلاش کنید');
      }
    }

    if (error && typeof error === 'object' && 'code' in error) {
      const networkError = error as { code: string };
      if (networkError.code === 'NETWORK_ERROR') {
        throw new Error('خطا در اتصال به سرور');
      }
    }

    throw new Error('خطا در ایجاد داشبورد');
  }
};

// Get all dashboards
export const getDashboards = async (): Promise<Dashboard[]> => {
  try {
    const response = await apiClient.get<DashboardsListResponse>('/dashboard/');

    if (!response.data.data) {
      throw new Error('Invalid response format');
    }

    return response.data.data.dashboards;
  } catch (error: unknown) {
    console.error('Get dashboards error:', error);

    // Handle different types of API errors
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as { response: { status: number; data?: any } };
      if (axiosError.response?.status === 401) {
        throw new Error('شما مجاز به مشاهده داشبوردها نیستید');
      } else if (axiosError.response?.status === 500) {
        throw new Error('خطا در سرور. لطفا بعدا تلاش کنید');
      }
    }

    if (error && typeof error === 'object' && 'code' in error) {
      const networkError = error as { code: string };
      if (networkError.code === 'NETWORK_ERROR') {
        throw new Error('خطا در اتصال به سرور');
      }
    }

    throw new Error('خطا در دریافت لیست داشبوردها');
  }
};

// Get dashboard by ID
export const getDashboardById = async (
  id: string | number
): Promise<Dashboard> => {
  try {
    const response = await apiClient.get<CreateDashboardResponse>(
      `/dashboard/${id}/`
    );

    if (!response.data.data) {
      throw new Error('Invalid response format');
    }

    return response.data.data;
  } catch (error: unknown) {
    console.error('Get dashboard by ID error:', error);

    // Handle different types of API errors
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as { response: { status: number; data?: any } };
      if (axiosError.response?.status === 404) {
        throw new Error('داشبورد مورد نظر یافت نشد');
      } else if (axiosError.response?.status === 401) {
        throw new Error('شما مجاز به مشاهده این داشبورد نیستید');
      } else if (axiosError.response?.status === 500) {
        throw new Error('خطا در سرور. لطفا بعدا تلاش کنید');
      }
    }

    if (error && typeof error === 'object' && 'code' in error) {
      const networkError = error as { code: string };
      if (networkError.code === 'NETWORK_ERROR') {
        throw new Error('خطا در اتصال به سرور');
      }
    }

    throw new Error('خطا در دریافت اطلاعات داشبورد');
  }
};

// Update dashboard
export const updateDashboard = async (
  id: string | number,
  payload: CreateDashboardPayload
): Promise<Dashboard> => {
  try {
    // Validate payload with Zod
    const validatedPayload = createDashboardSchema.parse(payload);

    console.log('Sending PUT request to:', `/dashboard/${id}/`);
    console.log('Validated payload:', validatedPayload);

    const response = await apiClient.put<CreateDashboardResponse>(
      `/dashboard/${id}/`,
      validatedPayload
    );

    console.log('Update response:', response.data);

    if (!response.data.data) {
      throw new Error('Invalid response format');
    }

    return response.data.data;
  } catch (error: unknown) {
    console.error('Update dashboard error:', error);

    // Handle Zod validation errors
    if (error instanceof z.ZodError) {
      const errorMessages = error.issues
        .map((issue) => issue.message)
        .join(', ');
      throw new Error(`خطا در اعتبارسنجی داده‌ها: ${errorMessages}`);
    }

    // Handle different types of API errors
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as { response: { status: number; data?: any } };
      if (axiosError.response?.status === 400) {
        throw new Error('اطلاعات وارد شده نامعتبر است');
      } else if (axiosError.response?.status === 401) {
        throw new Error('شما مجاز به انجام این عملیات نیستید');
      } else if (axiosError.response?.status === 404) {
        throw new Error('داشبورد مورد نظر یافت نشد');
      } else if (axiosError.response?.status === 500) {
        throw new Error('خطا در سرور. لطفا بعدا تلاش کنید');
      }
    }

    if (error && typeof error === 'object' && 'code' in error) {
      const networkError = error as { code: string };
      if (networkError.code === 'NETWORK_ERROR') {
        throw new Error('خطا در اتصال به سرور');
      }
    }

    throw new Error('خطا در به‌روزرسانی داشبورد');
  }
};

// Delete dashboard
export const deleteDashboard = async (id: string | number): Promise<void> => {
  try {
    await apiClient.delete(`/dashboard/${id}/`);
  } catch (error: unknown) {
    console.error('Delete dashboard error:', error);

    // Handle different types of API errors
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as { response: { status: number; data?: any } };
      if (axiosError.response?.status === 404) {
        throw new Error('داشبورد مورد نظر یافت نشد');
      } else if (axiosError.response?.status === 401) {
        throw new Error('شما مجاز به حذف این داشبورد نیستید');
      } else if (axiosError.response?.status === 500) {
        throw new Error('خطا در سرور. لطفا بعدا تلاش کنید');
      }
    }

    if (error && typeof error === 'object' && 'code' in error) {
      const networkError = error as { code: string };
      if (networkError.code === 'NETWORK_ERROR') {
        throw new Error('خطا در اتصال به سرور');
      }
    }

    throw new Error('خطا در حذف داشبورد');
  }
};

// Create widget
export const createWidget = async (
  dashboardId: string | number,
  payload: CreateWidgetPayload
): Promise<Widget> => {
  try {
    // Validate payload with Zod
    const validatedPayload = createWidgetSchema.parse(payload);

    const response = await apiClient.post<CreateWidgetResponse>(
      `/dashboard/${dashboardId}/widget/`,
      validatedPayload
    );

    if (!response.data.data) {
      throw new Error('Invalid response format');
    }

    return response.data.data;
  } catch (error: unknown) {
    console.error('Create widget error:', error);

    // Handle Zod validation errors
    if (error instanceof z.ZodError) {
      const errorMessages = error.issues
        .map((issue) => issue.message)
        .join(', ');
      throw new Error(`خطا در اعتبارسنجی داده‌ها: ${errorMessages}`);
    }

    // Handle different types of API errors
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as { response: { status: number; data?: any } };
      if (axiosError.response?.status === 400) {
        throw new Error('اطلاعات وارد شده نامعتبر است');
      } else if (axiosError.response?.status === 401) {
        throw new Error('شما مجاز به انجام این عملیات نیستید');
      } else if (axiosError.response?.status === 404) {
        throw new Error('داشبورد مورد نظر یافت نشد');
      } else if (axiosError.response?.status === 500) {
        throw new Error('خطا در سرور. لطفا بعدا تلاش کنید');
      }
    }

    if (error && typeof error === 'object' && 'code' in error) {
      const networkError = error as { code: string };
      if (networkError.code === 'NETWORK_ERROR') {
        throw new Error('خطا در اتصال به سرور');
      }
    }

    throw new Error('خطا در ایجاد گزارش');
  }
};

// Update widget positions
export const updateWidgetPositions = async (
  dashboardId: string | number,
  positions: Record<
    string,
    { x: number; y: number; width: number; height: number }
  >
): Promise<void> => {
  try {
    const response = await apiClient.put(
      `/dashboard/${dashboardId}/widget/position/`, // Sample endpoint - replace with actual
      positions
    );

    if (response.status !== 200) {
      throw new Error('Invalid response status');
    }
  } catch (error: unknown) {
    console.error('Update widget positions error:', error);

    // Handle different types of API errors
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as { response: { status: number; data?: any } };
      if (axiosError.response?.status === 400) {
        throw new Error('اطلاعات موقعیت نامعتبر است');
      } else if (axiosError.response?.status === 401) {
        throw new Error('شما مجاز به انجام این عملیات نیستید');
      } else if (axiosError.response?.status === 404) {
        throw new Error('داشبورد مورد نظر یافت نشد');
      } else if (axiosError.response?.status === 500) {
        throw new Error('خطا در سرور. لطفا بعدا تلاش کنید');
      }
    }

    if (error && typeof error === 'object' && 'code' in error) {
      const networkError = error as { code: string };
      if (networkError.code === 'NETWORK_ERROR') {
        throw new Error('خطا در اتصال به سرور');
      }
    }

    throw new Error('خطا در به‌روزرسانی موقعیت گزارش‌ها');
  }
};

// Fetch widget data
export const fetchWidgetData = async (
  payload: WidgetDataRequest
): Promise<WidgetData> => {
  try {
    const response = await apiClient.post<WidgetDataResponse>(
      '/search/', // Replace with actual endpoint
      payload
    );

    if (!response.data.data) {
      throw new Error('Invalid response format');
    }

    return response.data.data;
  } catch (error: unknown) {
    console.error('Fetch widget data error:', error);

    // Handle different types of API errors
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as { response: { status: number; data?: any } };
      if (axiosError.response?.status === 400) {
        throw new Error('پارامترهای درخواست نامعتبر است');
      } else if (axiosError.response?.status === 401) {
        throw new Error('شما مجاز به دریافت این داده‌ها نیستید');
      } else if (axiosError.response?.status === 404) {
        throw new Error('داده‌ای یافت نشد');
      } else if (axiosError.response?.status === 500) {
        throw new Error('خطا در سرور. لطفا بعدا تلاش کنید');
      }
    }

    if (error && typeof error === 'object' && 'code' in error) {
      const networkError = error as { code: string };
      if (networkError.code === 'NETWORK_ERROR') {
        throw new Error('خطا در اتصال به سرور');
      }
    }

    throw new Error('خطا در دریافت داده‌های گزارش');
  }
};

// Update widget
export const updateWidget = async (
  dashboardId: string | number,
  widgetId: string | number,
  payload: CreateWidgetPayload
): Promise<Widget> => {
  try {
    // Validate payload with Zod
    const validatedPayload = createWidgetSchema.parse(payload);

    const response = await apiClient.put<CreateWidgetResponse>(
      `/dashboard/${dashboardId}/widget/${widgetId}/`,
      validatedPayload
    );

    if (!response.data.data) {
      throw new Error('Invalid response format');
    }

    return response.data.data;
  } catch (error: unknown) {
    console.error('Update widget error:', error);

    // Handle Zod validation errors
    if (error instanceof z.ZodError) {
      const errorMessages = error.issues
        .map((issue) => issue.message)
        .join(', ');
      throw new Error(`خطا در اعتبارسنجی داده‌ها: ${errorMessages}`);
    }

    // Handle different types of API errors
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as { response: { status: number; data?: any } };
      if (axiosError.response?.status === 404) {
        throw new Error('ویجت مورد نظر یافت نشد');
      } else if (axiosError.response?.status === 401) {
        throw new Error('شما مجاز به ویرایش این ویجت نیستید');
      } else if (axiosError.response?.status === 500) {
        throw new Error('خطا در سرور. لطفا بعدا تلاش کنید');
      }
    }

    if (error && typeof error === 'object' && 'code' in error) {
      const networkError = error as { code: string };
      if (networkError.code === 'NETWORK_ERROR') {
        throw new Error('خطا در اتصال به سرور');
      }
    }

    throw new Error('خطا در به‌روزرسانی ویجت');
  }
};

// Get widget by ID
export const getWidgetById = async (
  dashboardId: string | number,
  widgetId: string | number
): Promise<Widget> => {
  try {
    // Get the dashboard first, then find the widget
    const dashboard = await getDashboardById(dashboardId);
    const widget = dashboard.widgets?.find(
      (w) => w.id.toString() === widgetId.toString()
    );

    if (!widget) {
      throw new Error('ویجت مورد نظر یافت نشد');
    }

    return widget;
  } catch (error: unknown) {
    console.error('Get widget by ID error:', error);

    // Handle different types of API errors
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as { response: { status: number; data?: any } };
      if (axiosError.response?.status === 404) {
        throw new Error('ویجت مورد نظر یافت نشد');
      } else if (axiosError.response?.status === 401) {
        throw new Error('شما مجاز به مشاهده این ویجت نیستید');
      } else if (axiosError.response?.status === 500) {
        throw new Error('خطا در سرور. لطفا بعدا تلاش کنید');
      }
    }

    if (error && typeof error === 'object' && 'code' in error) {
      const networkError = error as { code: string };
      if (networkError.code === 'NETWORK_ERROR') {
        throw new Error('خطا در اتصال به سرور');
      }
    }

    // Re-throw if it's already a custom error message
    if (error instanceof Error) {
      throw error;
    }

    throw new Error('خطا در دریافت اطلاعات ویجت');
  }
};

// Delete widget
export const deleteWidget = async (
  dashboardId: string | number,
  widgetId: string | number
): Promise<void> => {
  try {
    await apiClient.delete(`/dashboard/${dashboardId}/widget/${widgetId}/`);
  } catch (error: unknown) {
    console.error('Delete widget error:', error);

    // Handle different types of API errors
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as { response: { status: number; data?: any } };
      if (axiosError.response?.status === 404) {
        throw new Error('ویجت مورد نظر یافت نشد');
      } else if (axiosError.response?.status === 401) {
        throw new Error('شما مجاز به حذف این ویجت نیستید');
      } else if (axiosError.response?.status === 500) {
        throw new Error('خطا در سرور. لطفا بعدا تلاش کنید');
      }
    }

    if (error && typeof error === 'object' && 'code' in error) {
      const networkError = error as { code: string };
      if (networkError.code === 'NETWORK_ERROR') {
        throw new Error('خطا در اتصال به سرور');
      }
    }

    throw new Error('خطا در حذف ویجت');
  }
};

// Upload dashboard preview image
export const uploadDashboardPreview = async (
  file: File | Blob
): Promise<{
  image: string;
  width: number;
  height: number;
  file_hash: string;
  file_size: number;
  uploaded_at: string;
}> => {
  try {
    const formData = new FormData();

    // Create a proper File object if we received a Blob
    let fileToUpload: File;
    if (file instanceof Blob && !(file instanceof File)) {
      fileToUpload = new File([file], 'dashboard-preview.png', {
        type: 'image/png',
        lastModified: Date.now(),
      });
    } else {
      fileToUpload = file as File;
    }

    formData.append('image', fileToUpload);

    console.log('Uploading dashboard preview...', {
      fileName: fileToUpload.name,
      fileSize: fileToUpload.size,
      fileType: fileToUpload.type,
    });

    const response = await apiClient.post(
      '/dashboard/preview/upload/',
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );

    console.log('Upload preview response:', response.data);

    return response.data;
  } catch (error: unknown) {
    console.error('Upload preview error:', error);

    // Handle different types of API errors
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as { response: { status: number; data?: any } };
      console.error('Server response:', axiosError.response?.data);
      console.error('Server status:', axiosError.response?.status);

      if (axiosError.response?.status === 400) {
        // Log the exact server error message for debugging
        console.error('400 Bad Request details:', axiosError.response?.data);
        throw new Error('فایل تصویر نامعتبر است');
      } else if (axiosError.response?.status === 401) {
        throw new Error('شما مجاز به آپلود تصویر نیستید');
      } else if (axiosError.response?.status === 413) {
        throw new Error('حجم فایل خیلی بزرگ است');
      } else if (axiosError.response?.status === 500) {
        throw new Error('خطا در سرور. لطفا بعدا تلاش کنید');
      }
    }

    if (error && typeof error === 'object' && 'code' in error) {
      const networkError = error as { code: string };
      if (networkError.code === 'NETWORK_ERROR') {
        throw new Error('خطا در اتصال به سرور');
      }
    }

    throw new Error('خطا در آپلود تصویر');
  }
};

// Update dashboard preview
export const updateDashboardPreview = async (
  id: string | number,
  previewUrl: string
): Promise<Dashboard> => {
  try {
    console.log('Updating dashboard preview:', id, previewUrl);

    const response = await apiClient.put<CreateDashboardResponse>(
      `/dashboard/${id}/`,
      { preview: previewUrl }
    );

    console.log('Update preview response:', response.data);

    if (!response.data.data) {
      throw new Error('Invalid response format');
    }

    return response.data.data;
  } catch (error: unknown) {
    console.error('Update dashboard preview error:', error);

    // Handle different types of API errors
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as { response: { status: number; data?: any } };
      if (axiosError.response?.status === 400) {
        throw new Error('اطلاعات تصویر نامعتبر است');
      } else if (axiosError.response?.status === 401) {
        throw new Error('شما مجاز به ویرایش این داشبورد نیستید');
      } else if (axiosError.response?.status === 404) {
        throw new Error('داشبورد مورد نظر یافت نشد');
      } else if (axiosError.response?.status === 500) {
        throw new Error('خطا در سرور. لطفا بعدا تلاش کنید');
      }
    }

    if (error && typeof error === 'object' && 'code' in error) {
      const networkError = error as { code: string };
      if (networkError.code === 'NETWORK_ERROR') {
        throw new Error('خطا در اتصال به سرور');
      }
    }

    throw new Error('خطا در به‌روزرسانی تصویر داشبورد');
  }
};
